2025-06-14T15:38:21.058+08:00  INFO 7084 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 7084 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T15:38:21.059+08:00 DEBUG 7084 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T15:38:21.059+08:00  INFO 7084 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T15:38:21.505+08:00  WARN 7084 --- [Novel] [main] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[org.example.novel.mapper]' package. Please check your configuration.
2025-06-14T15:38:21.686+08:00  INFO 7084 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port 8080 (http)
2025-06-14T15:38:21.693+08:00  INFO 7084 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T15:38:21.693+08:00  INFO 7084 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T15:38:21.721+08:00  INFO 7084 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T15:38:21.721+08:00  INFO 7084 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 637 ms
2025-06-14T15:38:22.155+08:00  WARN 7084 --- [Novel] [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6615d926-1cc1-4fb1-a16c-9cbaef11cf83

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-14T15:38:22.162+08:00  INFO 7084 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-14T15:38:22.247+08:00  INFO 7084 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T15:38:22.252+08:00  INFO 7084 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.439 seconds (process running for 1.775)
2025-06-14T16:03:21.902+08:00  INFO 7084 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:03:21.908+08:00  INFO 7084 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-06-14T16:03:26.427+08:00  INFO 19116 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 19116 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:03:26.428+08:00 DEBUG 19116 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:03:26.428+08:00  INFO 19116 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:03:26.852+08:00  WARN 19116 --- [Novel] [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'openAPI' defined in class path resource [org/example/novel/config/SwaggerConfig.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=swaggerConfig; factoryMethodName=openAPI; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/example/novel/config/SwaggerConfig.class]] for bean 'openAPI' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=openApiConfig; factoryMethodName=openAPI; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/example/novel/config/OpenApiConfig.class]] bound.
2025-06-14T16:03:26.855+08:00  INFO 19116 --- [Novel] [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-14T16:03:26.862+08:00 ERROR 19116 --- [Novel] [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'openAPI', defined in class path resource [org/example/novel/config/SwaggerConfig.class], could not be registered. A bean with that name has already been defined in class path resource [org/example/novel/config/OpenApiConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

2025-06-14T16:06:30.658+08:00  INFO 47908 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 47908 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:06:30.659+08:00 DEBUG 47908 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:06:30.660+08:00  INFO 47908 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:06:31.266+08:00  INFO 47908 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-14T16:06:31.274+08:00  INFO 47908 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T16:06:31.274+08:00  INFO 47908 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T16:06:31.299+08:00  INFO 47908 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T16:06:31.299+08:00  INFO 47908 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 611 ms
2025-06-14T16:06:31.450+08:00 DEBUG 47908 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14T16:06:31.472+08:00  INFO 47908 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14T16:06:31.954+08:00  INFO 47908 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T16:06:31.958+08:00  INFO 47908 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.546 seconds (process running for 1.833)
2025-06-14T16:13:27.714+08:00  INFO 47908 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:13:27.731+08:00  INFO 47908 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-06-14T16:13:31.072+08:00  INFO 37728 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 37728 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:13:31.073+08:00 DEBUG 37728 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:13:31.073+08:00  INFO 37728 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:13:31.668+08:00  INFO 37728 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-14T16:13:31.677+08:00  INFO 37728 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T16:13:31.677+08:00  INFO 37728 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T16:13:31.707+08:00  INFO 37728 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T16:13:31.707+08:00  INFO 37728 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 607 ms
2025-06-14T16:13:31.834+08:00 DEBUG 37728 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14T16:13:31.857+08:00  INFO 37728 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14T16:13:32.320+08:00  INFO 37728 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T16:13:32.324+08:00  INFO 37728 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.485 seconds (process running for 1.753)
2025-06-14T16:13:36.391+08:00  INFO 37728 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-14T16:13:36.391+08:00  INFO 37728 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-14T16:13:36.392+08:00  INFO 37728 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-14T16:13:36.401+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.432+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.460+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.461+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.461+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.461+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.461+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.582+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.583+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.599+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:36.698+08:00  INFO 37728 --- [Novel] [http-nio-8080-exec-10] o.springdoc.api.AbstractOpenApiResource  : Init duration for springdoc-openapi is: 94 ms
2025-06-14T16:13:47.251+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:13:51.337+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.863+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.883+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.883+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.883+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.883+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.883+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.941+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:14:32.949+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:47.460+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.874+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.892+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.892+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.892+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.893+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.893+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.956+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:49.964+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:16:53.876+08:00  WARN 37728 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:14.097+08:00  INFO 37728 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:28:14.103+08:00  INFO 37728 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-06-14T16:28:17.669+08:00  INFO 42328 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 42328 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:28:17.670+08:00 DEBUG 42328 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:28:17.671+08:00  INFO 42328 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:28:18.297+08:00  INFO 42328 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-14T16:28:18.304+08:00  INFO 42328 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T16:28:18.304+08:00  INFO 42328 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T16:28:18.331+08:00  INFO 42328 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T16:28:18.331+08:00  INFO 42328 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 632 ms
2025-06-14T16:28:18.472+08:00 DEBUG 42328 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14T16:28:18.495+08:00  INFO 42328 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14T16:28:18.959+08:00  INFO 42328 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T16:28:18.964+08:00  INFO 42328 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.521 seconds (process running for 1.794)
2025-06-14T16:28:34.455+08:00  INFO 42328 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-14T16:28:34.455+08:00  INFO 42328 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-14T16:28:34.456+08:00  INFO 42328 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-14T16:28:34.471+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.519+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.521+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.521+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.521+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.521+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.652+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.653+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:34.704+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.110+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.125+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.150+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.150+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.151+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.151+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.151+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.209+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:28:51.220+08:00  WARN 42328 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:07.193+08:00  INFO 42328 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:32:07.200+08:00  INFO 42328 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-06-14T16:32:10.590+08:00  INFO 26980 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 26980 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:32:10.591+08:00 DEBUG 26980 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:32:10.591+08:00  INFO 26980 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:32:11.237+08:00  INFO 26980 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-14T16:32:11.244+08:00  INFO 26980 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T16:32:11.245+08:00  INFO 26980 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T16:32:11.270+08:00  INFO 26980 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T16:32:11.270+08:00  INFO 26980 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 653 ms
2025-06-14T16:32:11.415+08:00 DEBUG 26980 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14T16:32:11.437+08:00  INFO 26980 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14T16:32:11.903+08:00  INFO 26980 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T16:32:11.908+08:00  INFO 26980 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.552 seconds (process running for 1.822)
2025-06-14T16:32:16.400+08:00  INFO 26980 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-14T16:32:16.400+08:00  INFO 26980 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-14T16:32:16.400+08:00  INFO 26980 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-06-14T16:32:16.410+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.447+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.448+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.448+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.448+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.448+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.574+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:32:16.609+08:00  WARN 26980 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:27.211+08:00  INFO 26980 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:40:27.219+08:00  INFO 26980 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-06-14T16:40:30.249+08:00  INFO 2316 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 2316 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:40:30.250+08:00 DEBUG 2316 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:40:30.250+08:00  INFO 2316 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:40:30.870+08:00  INFO 2316 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-14T16:40:30.877+08:00  INFO 2316 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T16:40:30.878+08:00  INFO 2316 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T16:40:30.905+08:00  INFO 2316 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T16:40:30.906+08:00  INFO 2316 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 624 ms
2025-06-14T16:40:31.045+08:00 DEBUG 2316 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14T16:40:31.068+08:00  INFO 2316 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14T16:40:31.538+08:00  INFO 2316 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T16:40:31.542+08:00  INFO 2316 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.524 seconds (process running for 1.795)
2025-06-14T16:40:38.975+08:00  INFO 2316 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-14T16:40:38.975+08:00  INFO 2316 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-14T16:40:38.976+08:00  INFO 2316 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-14T16:40:38.985+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.021+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.022+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.022+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.022+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.022+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.147+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.148+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:40:39.171+08:00  WARN 2316 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:42:05.458+08:00  INFO 2316 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:42:05.462+08:00  INFO 2316 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-06-14T16:44:25.728+08:00  INFO 45648 --- [Novel] [main] org.example.novel.NovelApplication       : Starting NovelApplication using Java 24.0.1 with PID 45648 (H:\code\program\Novel\target\classes started by 28968 in H:\code\program\Novel)
2025-06-14T16:44:25.729+08:00 DEBUG 45648 --- [Novel] [main] org.example.novel.NovelApplication       : Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-14T16:44:25.729+08:00  INFO 45648 --- [Novel] [main] org.example.novel.NovelApplication       : No active profile set, falling back to 1 default profile: "default"
2025-06-14T16:44:26.382+08:00  INFO 45648 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-14T16:44:26.389+08:00  INFO 45648 --- [Novel] [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-14T16:44:26.389+08:00  INFO 45648 --- [Novel] [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-14T16:44:26.414+08:00  INFO 45648 --- [Novel] [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-14T16:44:26.415+08:00  INFO 45648 --- [Novel] [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 660 ms
2025-06-14T16:44:26.544+08:00 DEBUG 45648 --- [Novel] [main] o.e.n.security.JwtAuthenticationFilter   : Filter 'jwtAuthenticationFilter' configured for use
2025-06-14T16:44:26.567+08:00  INFO 45648 --- [Novel] [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-06-14T16:44:27.036+08:00  INFO 45648 --- [Novel] [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-06-14T16:44:27.041+08:00  INFO 45648 --- [Novel] [main] org.example.novel.NovelApplication       : Started NovelApplication in 1.544 seconds (process running for 1.826)
2025-06-14T16:44:39.818+08:00  INFO 45648 --- [Novel] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-14T16:44:39.819+08:00  INFO 45648 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-14T16:44:39.819+08:00  INFO 45648 --- [Novel] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-06-14T16:44:39.835+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:39.885+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:39.885+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:39.885+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:39.885+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-4] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:39.885+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:40.021+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:44:40.061+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:10.314+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-6] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.045+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-3] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.076+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-8] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.077+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-10] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.077+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-9] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.077+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-1] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.077+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-7] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.161+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-5] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:48:29.169+08:00  WARN 45648 --- [Novel] [http-nio-8080-exec-2] o.e.n.security.JwtAuthenticationFilter   : JWT令牌不以Bearer开头或不存在
2025-06-14T16:52:40.983+08:00  INFO 45648 --- [Novel] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-06-14T16:52:40.989+08:00  INFO 45648 --- [Novel] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
