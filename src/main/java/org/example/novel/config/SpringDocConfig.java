package org.example.novel.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * SpringDoc配置类
 */
@Configuration
public class SpringDocConfig {
    
    @Value("${server.servlet.context-path:}")
    private String contextPath;
    
    /**
     * 配置OpenAPI服务器路径
     * 解决Swagger UI路径问题
     */
    @Bean
    public OpenAPI customOpenAPI(OpenAPI openAPI) {
        // 如果已有servers配置则不处理
        if (openAPI.getServers() != null && !openAPI.getServers().isEmpty()) {
            return openAPI;
        }
        
        // 添加服务器配置，确保API路径正确
        Server server = new Server()
                .url(contextPath)
                .description("API服务器");
        
        return openAPI.servers(List.of(server));
    }
} 