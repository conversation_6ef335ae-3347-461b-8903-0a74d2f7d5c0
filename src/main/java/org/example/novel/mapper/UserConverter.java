package org.example.novel.mapper;

import org.example.novel.dto.LoginResponseDTO;
import org.example.novel.dto.UserDTO;
import org.example.novel.entity.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 用户对象转换器
 */
@Mapper(componentModel = "spring")
public interface UserConverter {

    UserConverter INSTANCE = Mappers.getMapper(UserConverter.class);

    /**
     * 用户DTO转换为用户实体
     *
     * @param userDTO 用户DTO
     * @return 用户实体
     */
    User toEntity(UserDTO userDTO);

    /**
     * 用户实体转换为用户DTO
     *
     * @param user 用户实体
     * @return 用户DTO
     */
    @Mapping(target = "password", ignore = true)
    UserDTO toDTO(User user);

    /**
     * 用户实体转换为登录响应DTO
     *
     * @param user  用户实体
     * @param token JWT令牌
     * @return 登录响应DTO
     */
    @Mapping(target = "token", source = "token")
    LoginResponseDTO toLoginResponseDTO(User user, String token);
} 