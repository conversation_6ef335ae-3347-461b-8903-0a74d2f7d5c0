package org.example.novel.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
@Tag(name = "测试接口", description = "用于测试系统功能的接口")
public class TestController {

    /**
     * 测试接口
     *
     * @return 测试消息
     */
    @GetMapping("/hello")
    @Operation(summary = "测试接口", description = "返回一个简单的问候消息")
    public String hello() {
        return "Hello from Novel API!";
    }
} 