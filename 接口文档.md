# AI视频生成项目接口文档
http://localhost:8080/api/swagger-ui.html
## 1. 用户认证与授权接口

### 1.1 用户注册

- **接口URL**: `/api/auth/register`
- **请求方式**: POST
- **接口描述**: 用户注册接口，创建新用户账号
- **请求参数**:

```json
{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "USER"  // 可选，默认为"USER"
}
```

- **参数说明**:
  - `username`: 用户名，必填，长度4-20，只能包含字母、数字和下划线
  - `password`: 密码，必填，长度6-20
  - `email`: 邮箱，可选，但必须符合邮箱格式
  - `role`: 角色，可选，默认为"USER"，可选值："USER"(普通用户)、"VIP"(VIP用户)

- **响应示例**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.2 用户登录

- **接口URL**: `/api/auth/login`
- **请求方式**: POST
- **接口描述**: 用户登录接口，返回JWT令牌
- **请求参数**:

```json
{
  "username": "testuser",
  "password": "password123"
}
```

- **参数说明**:
  - `username`: 用户名，必填
  - `password`: 密码，必填

- **响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER",
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYxNjEyMzQ1NiwiZXhwIjoxNjE2MjA5ODU2fQ.hV6pGvAVV-Qg0XLzTJgGPz8I_lHvGJIJ9ysOQZGu1q4"
  }
}
```

### 1.3 获取当前用户信息

- **接口URL**: `/api/users/me`
- **请求方式**: GET
- **接口描述**: 获取当前登录用户的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.4 更新当前用户信息

- **接口URL**: `/api/users/me`
- **请求方式**: PUT
- **接口描述**: 更新当前登录用户的详细信息
- **请求头**:
  - `Authorization`: Bearer {token}
- **请求参数**:

```json
{
  "password": "newpassword123",
  "email": "<EMAIL>"
}
```

- **参数说明**:
  - `password`: 新密码，可选，长度6-20
  - `email`: 新邮箱，可选，但必须符合邮箱格式

- **响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.5 获取指定用户信息（需要VIP权限）

- **接口URL**: `/api/users/{id}`
- **请求方式**: GET
- **接口描述**: 根据用户ID获取用户详细信息，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 用户ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 2,
    "username": "anotheruser",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 1.6 删除用户（需要VIP权限）

- **接口URL**: `/api/users/{id}`
- **请求方式**: DELETE
- **接口描述**: 根据用户ID删除用户，需要VIP权限
- **请求头**:
  - `Authorization`: Bearer {token}
- **路径参数**:
  - `id`: 用户ID

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 错误响应

### 认证错误

```json
{
  "code": 401,
  "message": "未授权：用户名或密码错误",
  "data": null
}
```

### 权限错误

```json
{
  "code": 403,
  "message": "无权限访问",
  "data": null
}
```

### 参数错误

```json
{
  "code": 400,
  "message": "用户名不能为空, 密码长度必须在6-20之间",
  "data": null
}
```

### 业务错误

```json
{
  "code": 400,
  "message": "用户名已存在",
  "data": null
}
```

### 服务器错误

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```
